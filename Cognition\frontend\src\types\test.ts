// 测试相关类型定义

export interface TestQuestion {
  id: string
  text: string
  options: TestOption[]
  section: string
  order: number
}

export interface TestOption {
  value: number
  text: string
  description?: string
}

export interface TestAnswer {
  questionId: string
  value: number
  timestamp: number
}

export interface TestSession {
  id: string
  testType: string
  userId: string
  startTime: string
  endTime?: string
  currentSection: string
  currentQuestionIndex: number
  answers: TestAnswer[]
  status: 'not_started' | 'in_progress' | 'completed' | 'paused'
}

// PDQ-5 测试相关类型
export interface PDQ5Section {
  id: string
  name: string
  description: string
  instructions: string
  questions: TestQuestion[]
  options: TestOption[]
}

export interface PDQ5TestData {
  sections: PDQ5Section[]
  totalQuestions: number
}

// 语音播报相关
export interface VoiceSettings {
  enabled: boolean
  rate: number
  pitch: number
  volume: number
  voice?: SpeechSynthesisVoice
}

export interface TestInstructions {
  title: string
  content: string | string[]
  audioEnabled: boolean
  startButtonText: string
}
