<template>
  <div class="test-container">
    <!-- 头部 -->
    <div class="header-section">
      <button @click="handleExit" class="exit-button" aria-label="退出测试">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

      <div class="header-center">
        <!-- 全局计时器 -->
        <GlobalTimer position="title-left" size="medium" />
        
        <div class="progress-info">
          <h1 class="test-title">掩蔽情感启动任务</h1>
          <div class="progress-bar" v-if="!showInstructions">
            <div class="progress-fill" :style="{ width: `${progressPercentage}%` }"></div>
          </div>
          <div class="progress-text" v-if="!showInstructions">
            {{ currentEventIndex + 1 }} / {{ totalEvents }} 个事件
          </div>
        </div>
      </div>

      <div class="header-right">
        <!-- 主题切换按钮 -->
        <ThemeToggle />
      </div>
    </div>

    <!-- 指导语页面 -->
    <div v-if="showInstructions" class="instructions-screen">
      <div class="instructions-card">
        <h2 class="instructions-title">{{ instructions.title }}</h2>
        <div class="instructions-content">
          <p class="instructions-text">{{ instructions.content }}</p>
        </div>
        <div class="start-hint">
          <p>按任意键开始练习</p>
        </div>
      </div>
    </div>



    <!-- 测试进行中 -->
    <div v-else-if="testRunning" class="test-screen">
      <!-- 固定十字 -->
      <div v-if="currentPhase === 'cross'" class="cross-display">
        <div class="cross">+</div>
      </div>

      <!-- 黑屏 -->
      <div v-else-if="currentPhase === 'black'" class="black-screen"></div>

      <!-- 刺激图片 -->
      <div v-else-if="currentPhase === 'stimulus'" class="stimulus-display">
        <img
          :src="currentStimulusImage"
          alt="刺激图片"
          class="stimulus-image"
          @error="handleImageError"
          @load="handleImageLoad"
        />
      </div>

      <!-- Mask图片 -->
      <div v-else-if="currentPhase === 'mask'" class="mask-display">
        <img
          :src="currentMaskImage"
          alt="掩蔽图片"
          class="mask-image"
          @error="handleImageError"
          @load="handleImageLoad"
        />
      </div>

      <!-- 问题选择 -->
      <div v-else-if="currentPhase === 'question'" class="question-display">
        <div class="question-content">
          <!-- 正常问题界面 -->
            <!-- 问题标题 -->
            <div class="question-header">
              <h2 class="question-title">最后出现的表情传达的情绪是正面的还是负面的？</h2>
            </div>

          <!-- 情绪标签 -->
          <div class="emotion-labels">
            <div class="emotion-label left anger">
              <span class="emotion-text">负面</span>
              <span class="emotion-subtitle">愤怒</span>
            </div>
            <div class="emotion-arrow">
              <svg width="120" height="20" viewBox="0 0 120 20" fill="none">
                <defs>
                  <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#22c55e;stop-opacity:1" />
                  </linearGradient>
                </defs>
                <path d="M10 10L110 10" stroke="url(#arrowGradient)" stroke-width="2"/>
                <path d="M5 5L10 10L5 15" stroke="url(#arrowGradient)" stroke-width="2" fill="none"/>
                <path d="M115 5L110 10L115 15" stroke="url(#arrowGradient)" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <div class="emotion-label right happiness">
              <span class="emotion-text">正面</span>
              <span class="emotion-subtitle">快乐</span>
            </div>
          </div>

          <!-- 数字选择按钮 -->
          <div class="number-options">
            <div
              v-for="option in responseOptions"
              :key="option.value"
              class="number-button"
              :class="{ selected: selectedResponse === option.value }"
              @click="selectResponse(option.value)"
              :title="option.description"
            >
              {{ option.text }}
            </div>
          </div>

          <!-- 提醒文字和开始测试按钮 -->
          <div v-if="showReminder" class="reminder-section">
            <div class="reminder-text">
              请选择！！！
            </div>
            <!-- 第二个练习事件时显示开始正式测试按钮 -->
            <div v-if="isPractice && currentEventIndex === 1" class="start-test-section">
              <button @click="startFormalTest" class="start-test-button">
                开始正式测试
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试完成 -->
    <div v-else-if="testCompleted" class="completion-screen">
      <div class="completion-card">
        <h2>测试完成</h2>
        <p>感谢您完成掩蔽情感启动任务！</p>
        <button @click="goToHome" class="complete-button">返回首页</button>
      </div>
    </div>

    <!-- 退出确认对话框 -->
    <div v-if="showExitDialog" class="exit-dialog-overlay">
      <div class="exit-dialog">
        <h3>确认退出</h3>
        <p>您确定要退出测试吗？当前进度将会丢失。</p>
        <div class="dialog-buttons">
          <button @click="cancelExit" class="cancel-button">取消</button>
          <button @click="confirmExit" class="confirm-button">确认退出</button>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  maskedEmotionInstructions,
  generateEventSequence,
  responseOptions,
  testConfig,
  imageSet,
  type MaskedEmotionEvent
} from '@/data/masked-emotion-test'
import { GlobalTimerService } from '@/utils/globalTimer'
import ThemeToggle from '@/components/ThemeToggle.vue'
import GlobalTimer from '@/components/GlobalTimer.vue'

const router = useRouter()

// 响应式数据
const showInstructions = ref(true)
const showFormalStart = ref(false)
const testRunning = ref(false)
const testCompleted = ref(false)
const showExitDialog = ref(false)
const isPractice = ref(true)

const currentEventIndex = ref(0)
const currentPhase = ref<'cross' | 'black' | 'stimulus' | 'mask' | 'question'>('cross')
const currentCycleIndex = ref(0) // 当前循环索引 (0-2)
const selectedResponse = ref<number | null>(null)
const showReminder = ref(false)

const currentStimulusImage = ref('')
const currentMaskImage = ref('')

// 测试数据
const instructions = maskedEmotionInstructions
const practiceEvents = ref<MaskedEmotionEvent[]>([])
const formalEvents = ref<MaskedEmotionEvent[]>([])
const currentEvents = computed(() => isPractice.value ? practiceEvents.value : formalEvents.value)

// 计算属性
const totalEvents = computed(() => 
  isPractice.value ? testConfig.practiceEvents : testConfig.totalEvents
)

const progressPercentage = computed(() => {
  if (currentEvents.value.length === 0) return 0
  return Math.round((currentEventIndex.value / currentEvents.value.length) * 100)
})

const currentEvent = computed(() => currentEvents.value[currentEventIndex.value])

// 定时器引用
let phaseTimer: ReturnType<typeof setTimeout> | null = null
let reminderTimer: ReturnType<typeof setTimeout> | null = null

// 图片预加载
const preloadImages = async () => {
  const allImages = [
    ...imageSet.happy,
    ...imageSet.angry,
    ...imageSet.neutral,
    ...imageSet.mask
  ]

  const loadPromises = allImages.map(src => {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve(src)
      img.onerror = () => {
        console.warn(`Failed to load image: ${src}`)
        // 即使某些图片加载失败，也继续执行
        resolve(src)
      }
      img.src = src
    })
  })

  try {
    await Promise.all(loadPromises)
    console.log('Images preloaded successfully')
  } catch (error) {
    console.error('Error preloading images:', error)
  }
}

// 初始化
onMounted(async () => {
  // 预加载图片
  await preloadImages()

  // 生成事件序列
  practiceEvents.value = generateEventSequence(true)
  formalEvents.value = generateEventSequence(false)

  // 监听键盘事件
  document.addEventListener('keydown', handleKeyPress)

  // 启动全局计时器
  GlobalTimerService.start()
})

// 清理
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyPress)
  clearTimers()
})

// 清理定时器
const clearTimers = () => {
  if (phaseTimer) {
    clearTimeout(phaseTimer)
    phaseTimer = null
  }
  if (reminderTimer) {
    clearTimeout(reminderTimer)
    reminderTimer = null
  }
}

// 键盘事件处理
const handleKeyPress = (event: KeyboardEvent) => {
  if (showInstructions.value) {
    // 指导语页面，任意键开始练习
    startPractice()
  } else if (currentPhase.value === 'question') {
    // 问题阶段，数字键选择 (1-9)
    const key = event.key
    if (['1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(key)) {
      selectResponse(parseInt(key))
    } else if (isPractice.value && currentEventIndex.value === 1 && showReminder.value) {
      // 第二个练习事件中，当显示提醒时，任意按键开始正式测试
      startFormalTest()
    }
  }
}

// 开始练习
const startPractice = () => {
  showInstructions.value = false
  testRunning.value = true
  isPractice.value = true
  currentEventIndex.value = 0
  startEvent()
}

// 开始正式测试
const startFormalTest = () => {
  showFormalStart.value = false
  testRunning.value = true
  isPractice.value = false
  currentEventIndex.value = 0
  startEvent()
}

// 开始事件
const startEvent = () => {
  if (!currentEvent.value) return

  // 记录事件开始时间
  currentEvent.value.onsetTime = Date.now()

  // 重置状态
  currentCycleIndex.value = 0
  selectedResponse.value = null
  showReminder.value = false

  // 开始第一个循环
  startCycle()
}

// 开始循环 (每个事件包含3个循环)
const startCycle = () => {
  // 阶段1: 固定十字 (0-1000ms)
  currentPhase.value = 'cross'
  phaseTimer = setTimeout(() => {
    // 阶段2: 黑屏 (1001-1500ms)
    currentPhase.value = 'black'
    phaseTimer = setTimeout(() => {
      // 阶段3: 刺激图片 (1501-1533ms)
      currentPhase.value = 'stimulus'
      currentStimulusImage.value = currentEvent.value?.stimulusImages[currentCycleIndex.value] || ''
      phaseTimer = setTimeout(() => {
        // 阶段4: 黑屏 (1534-1565ms)
        currentPhase.value = 'black'
        phaseTimer = setTimeout(() => {
          // 阶段5: Mask图片 (1566-2000ms)
          currentPhase.value = 'mask'
          currentMaskImage.value = currentEvent.value?.maskImages[currentCycleIndex.value] || ''
          phaseTimer = setTimeout(() => {
            // 检查是否需要继续下一个循环
            currentCycleIndex.value++
            if (currentCycleIndex.value < 3) {
              startCycle() // 继续下一个循环
            } else {
              startQuestionPhase() // 开始问题阶段
            }
          }, testConfig.maskDuration) // 434ms
        }, 32) // 32ms黑屏
      }, testConfig.stimulusDuration) // 33ms刺激
    }, testConfig.blackDuration) // 500ms黑屏
  }, testConfig.crossDuration) // 1000ms十字
}

// 开始问题阶段
const startQuestionPhase = () => {
  currentPhase.value = 'question'

  // 设置提醒定时器 (3秒后提醒)
  reminderTimer = setTimeout(() => {
    showReminder.value = true
  }, 3000) // 3秒后显示提醒

  // 设置问题阶段结束定时器 (4秒)
  phaseTimer = setTimeout(() => {
    // 如果是第二个练习事件，不自动进入下一个事件，等待用户主动触发
    if (isPractice.value && currentEventIndex.value === 1) {
      // 第二个练习事件，不自动进入下一个事件
      return
    }
    nextEvent()
  }, testConfig.questionDuration) // 4000ms
}

// 选择回答
const selectResponse = (value: number) => {
  if (currentPhase.value !== 'question' || !currentEvent.value) return

  selectedResponse.value = value
  currentEvent.value.response = value
  currentEvent.value.responseTime = Date.now() - (currentEvent.value.onsetTime || 0)

  // 清除定时器
  clearTimers()

  // 延迟一下显示选择效果，然后进入下一个事件
  setTimeout(() => {
    nextEvent()
  }, 300)
}

// 下一个事件
const nextEvent = () => {
  currentEventIndex.value++

  if (currentEventIndex.value >= currentEvents.value.length) {
    // 当前阶段完成
    if (isPractice.value) {
      // 练习完成，但不自动进入正式测试，等待用户在第二个练习事件中主动触发
      // 这里不做任何操作，让用户在第二个练习事件的提醒阶段主动选择
      clearTimers()
    } else {
      // 正式测试完成
      completeTest()
    }
  } else {
    // 继续下一个事件
    startEvent()
  }
}

// 完成测试
const completeTest = () => {
  testRunning.value = false
  testCompleted.value = true

  // 停止全局计时器
  GlobalTimerService.stop()

  // 这里可以保存测试结果
  console.log('掩蔽情感启动任务完成')
  console.log('练习结果:', practiceEvents.value)
  console.log('正式测试结果:', formalEvents.value)
}

// 退出相关
const handleExit = () => {
  showExitDialog.value = true
}

const cancelExit = () => {
  showExitDialog.value = false
}

const confirmExit = () => {
  GlobalTimerService.stop()
  clearTimers()
  router.push('/home')
}

const goToHome = () => {
  router.push('/home')
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  const originalSrc = img.src
  console.error('Failed to load webp image:', originalSrc)

  // 由于所有图片都已转换为webp格式，如果加载失败可能是文件不存在或路径错误
  // 可以在这里添加备用图片或其他处理逻辑
}

// 图片加载成功处理
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  console.log('Image loaded successfully:', img.src)
}


</script>

<style lang="scss" scoped>
.test-container {
  height: 100vh;
  background: var(--background);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
  background: var(--card);
  border-bottom: 1px solid var(--border);
  flex-shrink: 0;
}

.exit-button {
  background: var(--destructive);
  border: none;
  border-radius: 12px;
  padding: $spacing-sm;
  color: var(--destructive-foreground);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--destructive-hover);
    transform: scale(1.05);
  }
}

.header-center {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex: 1;
  justify-content: center;
}

.progress-info {
  text-align: center;
}

.test-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 $spacing-xs 0;
}

.progress-bar {
  width: 200px;
  height: 6px;
  background: var(--muted);
  border-radius: 3px;
  overflow: hidden;
  margin: $spacing-xs 0;
}

.progress-fill {
  height: 100%;
  background: var(--primary);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: $font-size-sm;
  color: var(--muted-foreground);
}

.header-right {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

// 指导语页面
.instructions-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
}

.instructions-card {
  background: var(--card);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border);
  padding: $spacing-xl;
  width: 80%;
  height: 80%;
  max-width: 1000px;
  max-height: 800px;
  text-align: center;
  animation: fadeIn 0.6s ease-out;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
}

.instructions-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 $spacing-lg 0;
}

.instructions-content {
  margin-bottom: $spacing-lg;
}

.instructions-text {
  font-size: $font-size-base;
  line-height: 1.6;
  color: var(--foreground);
  margin: 0 0 $spacing-md 0;
}

.start-hint {
  padding: $spacing-md;
  background: var(--primary);
  color: var(--primary-foreground);
  border-radius: 12px;
  font-weight: 500;

  p {
    margin: 0;
  }
}



.start-button {
  background: var(--primary);
  border: none;
  border-radius: 16px;
  padding: 16px 32px;
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--primary-foreground);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
  }
}

// 测试屏幕
.test-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000; // 黑色背景
}

.cross-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.cross {
  width: 258px;
  height: 298px;
  color: white;
  font-weight: bold;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 120px; // 调整字体大小以适应容器
  line-height: 1;
}

.black-screen {
  width: 100%;
  height: 100%;
  background: #000;
}

.stimulus-display,
.mask-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.stimulus-image,
.mask-image {
  max-width: 400px;
  max-height: 400px;
  object-fit: contain;
  user-select: none;
  pointer-events: none;
}

.question-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #000; // 黑色背景，与参考图片一致
  padding: $spacing-lg;
}

.question-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 800px;
  text-align: center;
  position: relative;
}

.question-header {
  margin-bottom: $spacing-xl;
}

.question-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.4;
}

.emotion-labels {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-lg;
  margin-bottom: $spacing-xl * 2;
  width: 100%;
}

.emotion-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;

  &.left {
    text-align: right;
  }

  &.right {
    text-align: left;
  }

  &.anger {
    color: #ef4444; // 红色代表愤怒

    .emotion-text {
      color: #ef4444;
    }

    .emotion-subtitle {
      color: #ef4444;
      opacity: 0.8;
    }
  }

  &.happiness {
    color: #22c55e; // 绿色代表快乐

    .emotion-text {
      color: #22c55e;
    }

    .emotion-subtitle {
      color: #22c55e;
      opacity: 0.8;
    }
  }
}

.emotion-text {
  font-size: $font-size-lg;
  font-weight: 600;
  margin-bottom: $spacing-xs;
}

.emotion-subtitle {
  font-size: $font-size-base;
  opacity: 0.8;
}

.emotion-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 $spacing-lg;
}

.number-options {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-md;
  margin-bottom: $spacing-xl;
  flex-wrap: wrap;
}

.number-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: white;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-xl;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  border: 3px solid transparent;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
  }

  &.selected {
    background: var(--primary);
    color: white;
    border-color: white;
    transform: scale(1.15);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.4);
  }
}

.reminder-section {
  position: absolute;
  bottom: -120px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-md;
}

.reminder-text {
  background: var(--destructive);
  color: var(--destructive-foreground);
  padding: $spacing-sm $spacing-lg;
  border-radius: 12px;
  font-size: $font-size-lg;
  font-weight: bold;
  animation: pulse 1s infinite;
  white-space: nowrap;
}

.start-test-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-xs;
}

.start-test-button {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border: none;
  border-radius: 8px;
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-base;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
  white-space: nowrap;

  &:hover {
    background: linear-gradient(135deg, #16a34a, #15803d);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

// 完成页面
.completion-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
}

.completion-card {
  background: var(--card);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border);
  padding: $spacing-xl;
  width: 80%;
  height: 80%;
  max-width: 1000px;
  max-height: 800px;
  text-align: center;
  animation: fadeIn 0.6s ease-out;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-y: auto;

  h2 {
    font-size: $font-size-xl;
    font-weight: 600;
    color: var(--foreground);
    margin: 0 0 $spacing-md 0;
  }

  p {
    font-size: $font-size-base;
    color: var(--muted-foreground);
    margin: 0 0 $spacing-lg 0;
  }
}

.complete-button {
  background: var(--primary);
  border: none;
  border-radius: 16px;
  padding: 16px 32px;
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--primary-foreground);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
  }
}

// 退出对话框
.exit-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.exit-dialog {
  background: var(--card);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border);
  padding: $spacing-xl;
  max-width: 400px;
  width: 90%;
  text-align: center;
  animation: slideIn 0.3s ease-out;

  h3 {
    font-size: $font-size-lg;
    font-weight: 600;
    color: var(--foreground);
    margin: 0 0 $spacing-md 0;
  }

  p {
    font-size: $font-size-base;
    color: var(--muted-foreground);
    margin: 0 0 $spacing-lg 0;
    line-height: 1.5;
  }
}

.dialog-buttons {
  display: flex;
  gap: $spacing-md;
  justify-content: center;
}

.cancel-button,
.confirm-button {
  padding: 12px 24px;
  border-radius: 12px;
  font-size: $font-size-base;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 100px;
}

.cancel-button {
  background: var(--muted);
  color: var(--muted-foreground);

  &:hover {
    background: var(--accent);
    transform: translateY(-1px);
  }
}

.confirm-button {
  background: var(--destructive);
  color: var(--destructive-foreground);

  &:hover {
    background: var(--destructive-hover);
    transform: translateY(-1px);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .header-section {
    padding: $spacing-sm;
  }

  .instructions-card,
  .completion-card {
    padding: $spacing-lg;
    margin: $spacing-sm;
  }

  .question-content {
    padding: $spacing-sm;
    max-width: 100%;
  }

  .question-title {
    font-size: $font-size-lg;
  }

  .emotion-labels {
    gap: $spacing-sm;
    margin-bottom: $spacing-xl;
  }

  .emotion-text {
    font-size: $font-size-base;
  }

  .emotion-subtitle {
    font-size: $font-size-sm;
  }

  .emotion-arrow svg {
    width: 80px;
    height: 16px;
  }

  .number-options {
    gap: $spacing-sm;
    justify-content: center;
    max-width: 100%;
  }

  .number-button {
    width: 50px;
    height: 50px;
    font-size: $font-size-lg;
  }

  .stimulus-image,
  .mask-image {
    max-width: 300px;
    max-height: 300px;
  }

  .cross {
    width: 200px;
    height: 230px;
    font-size: 80px; // 移动端适当缩小
  }

  .reminder-section {
    bottom: -100px;
  }

  .reminder-text {
    font-size: $font-size-base;
    padding: $spacing-xs $spacing-md;
  }

  .start-test-button {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }
}






</style>
