// 掩蔽情感启动任务测试数据配置
import type { TestInstructions } from '@/types/test'

// 测试指导语
export const maskedEmotionInstructions: TestInstructions = {
  title: '掩蔽情感启动任务',
  content: '您将看到一个快速闪过的表情之后，紧接着出现一个持续时间较长的表情。您需要判断这个持续时间较长的表情传达的情绪是正面的（快乐）还是负面的（愤怒）。请使用1-9的数字进行评分：1表示极度负面，9表示极度正面，5表示中性。越正面，按越大的数字；越负面，按越小的数字。按任意键开始练习。',
  audioEnabled: true,
  startButtonText: '按任意键开始练习'
}

// 图片类型定义
export interface ImageSet {
  happy: string[]
  angry: string[]
  neutral: string[]
  mask: string[]
}

// 事件类型定义
export interface MaskedEmotionEvent {
  id: string
  group: 'A' | 'B' | 'C'
  stimulusImages: string[] // 3次刺激图片
  maskImages: string[] // 3次mask图片
  targetEmotion: 'happy' | 'angry' // 目标情绪
  onsetTime?: number // 事件开始时间
  response?: number // 用户选择 (1-4)
  responseTime?: number // 反应时间
}

// 根据实际文件生成图片路径的辅助函数
const generateImagePaths = (category: string, gender: 'AF' | 'AM'): string[] => {
  const images: string[] = []

  // 根据实际文件定义可用的图片编号
  const availableNumbers = {
    AF: [1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 16, 17, 18, 24, 27, 28, 30, 31, 33, 34, 36, 37],
    AM: [1, 6, 7, 8, 9, 10, 11, 12, 13, 17, 18, 19, 22, 24, 26, 27, 29, 30, 32, 33, 34, 35, 36, 37]
  }

  const numbers = availableNumbers[gender]
  numbers.forEach(num => {
    // 所有图片现在都是webp格式
    const basePath = `/images/掩蔽情感启动任务/${category}/${gender}${num}`
    images.push(`${basePath}.webp`)
  })

  return images
}



// 图片资源配置
export const imageSet: ImageSet = {
  happy: [
    ...generateImagePaths('快乐', 'AF'),
    ...generateImagePaths('快乐', 'AM')
  ],
  angry: [
    ...generateImagePaths('愤怒', 'AF'),
    ...generateImagePaths('愤怒', 'AM')
  ],
  neutral: [
    ...generateImagePaths('平静1', 'AF'),
    ...generateImagePaths('平静1', 'AM')
  ],
  mask: [
    ...generateImagePaths('平静2', 'AF'),
    ...generateImagePaths('平静2', 'AM')
  ]
}

// 随机选择图片的辅助函数
export const getRandomImage = (imageArray: string[]): string => {
  return imageArray[Math.floor(Math.random() * imageArray.length)]
}

// 生成事件序列的函数
export const generateEventSequence = (isPractice: boolean = false): MaskedEmotionEvent[] => {
  const events: MaskedEmotionEvent[] = []
  
  if (isPractice) {
    // 练习模式：2个事件
    events.push(
      {
        id: 'practice_1',
        group: 'A',
        stimulusImages: [],
        maskImages: [],
        targetEmotion: 'happy'
      },
      {
        id: 'practice_2',
        group: 'B',
        stimulusImages: [],
        maskImages: [],
        targetEmotion: 'angry'
      }
    )
  } else {
    // 正式测试：54个事件 (3轮 × 18个事件)
    const groups: ('A' | 'B' | 'C')[] = ['A', 'B', 'C']
    let eventId = 1
    
    for (let round = 0; round < 3; round++) {
      for (let groupIndex = 0; groupIndex < 3; groupIndex++) {
        for (let i = 0; i < 6; i++) {
          const group = groups[groupIndex]
          let targetEmotion: 'happy' | 'angry'
          
          // 根据组别确定目标情绪分布
          if (group === 'A') {
            targetEmotion = i < 3 ? 'happy' : 'angry'
          } else if (group === 'B') {
            targetEmotion = i % 2 === 0 ? 'happy' : 'angry'
          } else { // group === 'C'
            targetEmotion = i < 2 ? 'angry' : 'happy'
          }
          
          events.push({
            id: `event_${eventId}`,
            group,
            stimulusImages: [],
            maskImages: [],
            targetEmotion
          })
          
          eventId++
        }
      }
    }
  }
  
  // 为每个事件生成随机图片
  events.forEach(event => {
    event.stimulusImages = [
      getRandomImage(event.targetEmotion === 'happy' ? imageSet.happy : imageSet.angry),
      getRandomImage(event.targetEmotion === 'happy' ? imageSet.happy : imageSet.angry),
      getRandomImage(event.targetEmotion === 'happy' ? imageSet.happy : imageSet.angry)
    ]
    event.maskImages = [
      getRandomImage(imageSet.mask),
      getRandomImage(imageSet.mask),
      getRandomImage(imageSet.mask)
    ]
  })
  
  return events
}

// 评分选项 (1-9量表，更精确的情绪评估)
export const responseOptions = [
  { value: 1, text: '1', description: '极度负面' },
  { value: 2, text: '2', description: '很负面' },
  { value: 3, text: '3', description: '负面' },
  { value: 4, text: '4', description: '稍微负面' },
  { value: 5, text: '5', description: '中性' },
  { value: 6, text: '6', description: '稍微正面' },
  { value: 7, text: '7', description: '正面' },
  { value: 8, text: '8', description: '很正面' },
  { value: 9, text: '9', description: '极度正面' }
]

// 测试配置
export const testConfig = {
  eventDuration: 10000, // 每个事件10秒
  stimulusDuration: 33, // 刺激图片显示33ms
  maskDuration: 434, // mask图片显示434ms
  crossDuration: 1000, // 十字显示1000ms
  blackDuration: 500, // 黑屏500ms
  questionDuration: 4000, // 问题显示4000ms
  reminderTime: 9000, // 9秒后提醒选择
  practiceEvents: 2, // 练习事件数量
  totalEvents: 54 // 正式测试事件数量
}
