<template>
  <div class="test-list-container">
    <!-- 顶部导航 -->
    <header class="test-header">
      <button @click="$router.back()" class="back-button" aria-label="返回">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
        </svg>
      </button>
      <h1 class="page-title">认知测试</h1>
      <div class="header-spacer"></div>
    </header>

    <main class="test-content">
      <!-- 测试说明 -->
      <section class="test-intro">
        <div class="intro-card">
          <div class="intro-content">
            <h2 class="intro-title">认知能力评估</h2>
            <p class="intro-description">
              我们的认知测试套件旨在全面评估您的记忆力、注意力、思维能力等多个认知维度。
              每个测试都经过科学验证，能够为您提供准确的认知能力分析。
            </p>
          </div>
          <div class="intro-illustration">
            <svg width="80" height="80" viewBox="0 0 80 80" class="illustration-svg">
              <circle cx="40" cy="40" r="35" fill="var(--color-primary)" opacity="0.1"/>
              <path d="M25 40c0-8.284 6.716-15 15-15s15 6.716 15 15" stroke="var(--color-primary)" stroke-width="2" fill="none"/>
              <circle cx="32" cy="35" r="2" fill="var(--color-primary)"/>
              <circle cx="48" cy="35" r="2" fill="var(--color-primary)"/>
              <path d="M30 50c0-5.523 4.477-10 10-10s10 4.477 10 10" stroke="var(--color-primary)" stroke-width="2" fill="none"/>
            </svg>
          </div>
        </div>
      </section>

      <!-- 测试列表 -->
      <section class="tests-section">
        <h2 class="section-title">可用测试项目</h2>
        
        <div class="tests-grid">
          <div
            v-for="test in availableTests"
            :key="test.id"
            class="test-card"
            :class="{ disabled: !test.available }"
            @click="startTest(test)"
            role="button"
            :tabindex="test.available ? 0 : -1"
            :aria-disabled="!test.available"
            @keydown.enter="startTest(test)"
            @keydown.space="startTest(test)"
          >
            <div class="test-header">
              <div class="test-icon" :class="`icon-${test.type}`">
                <component :is="getTestIcon(test.type)" />
              </div>
              <div v-if="!test.available" class="disabled-badge">
                即将开放
              </div>
            </div>
            
            <div class="test-info">
              <h3 class="test-name">{{ test.name }}</h3>
              <p class="test-description">{{ test.description }}</p>
              
              <div class="test-meta">
                <div class="meta-item">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                    <path d="M7 0C3.134 0 0 3.134 0 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 12.6C3.685 12.6 1.4 10.315 1.4 7S3.685 1.4 7 1.4 12.6 3.685 12.6 7 10.315 12.6 7 12.6z"/>
                    <path d="M7.7 3.5H6.3v4.2l3.15 1.89.7-1.155L7.7 6.65V3.5z"/>
                  </svg>
                  {{ test.duration }}
                </div>
                <div class="meta-item">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                    <path d="M7 1C3.686 1 1 3.686 1 7s2.686 6 6 6 6-2.686 6-6-2.686-6-6-6zm2.5 7.5L7 10V4.5h1.5V8.5z"/>
                  </svg>
                  {{ test.difficulty }}
                </div>
              </div>
              
              <div class="test-features">
                <span
                  v-for="feature in test.features"
                  :key="feature"
                  class="feature-tag"
                >
                  {{ feature }}
                </span>
              </div>
            </div>
            
            <div class="test-action">
              <button
                :disabled="!test.available"
                class="start-btn"
                :class="{ disabled: !test.available }"
                :aria-label="`开始${test.name}测试`"
              >
                {{ test.available ? '开始测试' : '即将开放' }}
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 测试须知 -->
      <section class="guidelines-section">
        <div class="guidelines-card">
          <h3 class="guidelines-title">测试须知</h3>
          <div class="guidelines-content">
            <div class="guideline-item">
              <div class="guideline-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                </svg>
              </div>
              <div class="guideline-text">
                <h4>测试环境</h4>
                <p>请在安静、不被打扰的环境中进行测试，确保网络连接稳定</p>
              </div>
            </div>
            
            <div class="guideline-item">
              <div class="guideline-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                </svg>
              </div>
              <div class="guideline-text">
                <h4>注意力集中</h4>
                <p>测试过程中请保持专注，避免同时进行其他活动</p>
              </div>
            </div>
            
            <div class="guideline-item">
              <div class="guideline-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                </svg>
              </div>
              <div class="guideline-text">
                <h4>真实作答</h4>
                <p>请根据真实感受和第一直觉作答，无需过度思考</p>
              </div>
            </div>
            
            <div class="guideline-item">
              <div class="guideline-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                </svg>
              </div>
              <div class="guideline-text">
                <h4>完整完成</h4>
                <p>为了获得准确的评估结果，请尽量完成整个测试</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 模拟测试数据
const availableTests = ref([
  {
    id: 'pdq5',
    type: 'PDQ5',
    name: 'PDQ-5 测试',
    description: '评估记忆力、注意力和思维反应能力的综合测试',
    duration: '15-20分钟',
    difficulty: '中等',
    features: ['记忆评估', '注意力测试', '认知灵活性'],
    available: true
  },
  {
    id: 'hopkins',
    type: 'Hopkins',
    name: 'Hopkins 词汇学习',
    description: '通过图片识别和记忆测试评估学习和记忆能力',
    duration: '10-15分钟',
    difficulty: '简单',
    features: ['即时记忆', '延迟记忆', '图片识别'],
    available: true
  },
  {
    id: 'nback',
    type: 'NBack',
    name: '顺背/倒背测试',
    description: '测试工作记忆和数字序列记忆能力',
    duration: '8-12分钟',
    difficulty: '中等',
    features: ['工作记忆', '数字记忆', '认知负荷'],
    available: true
  },
  {
    id: 'stroop',
    type: 'Stroop',
    name: 'Stroop 色词测试',
    description: '评估抑制控制能力和注意力选择性',
    duration: '5-8分钟',
    difficulty: '中等',
    features: ['抑制控制', '注意选择', '反应速度'],
    available: true
  },
  {
    id: 'trail',
    type: 'Trail',
    name: '连线测试',
    description: '评估视觉搜索、处理速度和执行功能',
    duration: '10-15分钟',
    difficulty: '中等',
    features: ['执行功能', '视觉搜索', '处理速度'],
    available: true
  },
  {
    id: 'verbal',
    type: 'Verbal',
    name: '词语流畅性测试',
    description: '通过语言产出评估语言功能和执行控制',
    duration: '3-5分钟',
    difficulty: '简单',
    features: ['语言功能', '执行控制', '语义记忆'],
    available: true
  },
  {
    id: 'cpt',
    type: 'CPT',
    name: '持续性操作测试',
    description: '评估持续注意力和反应抑制能力',
    duration: '8-10分钟',
    difficulty: '中等',
    features: ['持续注意', '反应抑制', '警觉性'],
    available: true
  },
  {
    id: 'dsst',
    type: 'DSST',
    name: 'DSST 数字符号转换',
    description: '评估处理速度、工作记忆和视觉-运动协调',
    duration: '5-8分钟',
    difficulty: '中等',
    features: ['处理速度', '符号转换', '视觉运动'],
    available: true
  },
  {
    id: 'masked',
    type: 'Masked',
    name: '掩蔽情感启动任务',
    description: '评估潜意识情绪处理和情感认知能力',
    duration: '15-20分钟',
    difficulty: '困难',
    features: ['情绪识别', '潜意识处理', '情感认知'],
    available: true // 现已开放
  }
])

// 获取测试图标
const getTestIcon = (testType: string) => {
  const iconMap: { [key: string]: any } = {
    PDQ5: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' })
    ]),
    Hopkins: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z' })
    ]),
    NBack: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z' })
    ]),
    Stroop: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' })
    ]),
    Trail: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M9.01 14H2v2h7.01v3L13 15l-3.99-4v3zm5.98-1v-3L19 14l-4.01 4v-3H8v-2h6.99z' })
    ]),
    Verbal: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z' })
    ]),
    CPT: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z' }),
      h('path', { d: 'M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z' })
    ]),
    DSST: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13z' })
    ]),
    Masked: () => h('svg', { width: 24, height: 24, viewBox: '0 0 24 24', fill: 'currentColor' }, [
      h('path', { d: 'M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z' })
    ])
  }
  
  return iconMap[testType] || iconMap.PDQ5
}

// 开始测试
const startTest = (test: any) => {
  if (!test.available) {
    return
  }

  // 根据测试类型跳转到对应页面
  switch (test.id) {
    case 'pdq5':
      router.push('/tests/pdq5/instructions')
      break
    case 'masked':
      router.push('/tests/masked-emotion')
      break
    default:
      // 其他测试暂时显示提示
      alert(`即将开始 ${test.name}，请确保您已阅读测试须知`)
      break
  }
}
</script>

<style lang="scss" scoped>
.test-list-container {
  min-height: 100vh;
  background: var(--color-background);
}

.test-header {
  background: var(--color-card);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
}

.back-button {
  background: none;
  border: none;
  color: var(--color-muted-foreground);
  cursor: pointer;
  padding: $spacing-sm;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  
  &:hover {
    color: var(--color-foreground);
    background: var(--color-muted);
  }
}

.page-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
}

.header-spacer {
  width: 40px; // 与back-button保持平衡
}

.test-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-xl $spacing-lg;
}

.test-intro {
  margin-bottom: $spacing-2xl;
}

.intro-card {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-xl);
  padding: $spacing-2xl;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-xl;
  
  @media (max-width: $tablet) {
    flex-direction: column;
    text-align: center;
  }
}

.intro-title {
  font-size: $font-size-2xl;
  font-weight: 700;
  margin-bottom: $spacing-md;
}

.intro-description {
  font-size: $font-size-base;
  opacity: 0.9;
  line-height: 1.6;
}

.illustration-svg {
  flex-shrink: 0;
  
  @media (max-width: $tablet) {
    width: 60px;
    height: 60px;
  }
}

.section-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--color-foreground);
  margin-bottom: $spacing-lg;
}

.tests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-2xl;
}

.test-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: $spacing-xl;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover:not(.disabled) {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-primary);
  }
  
  &:focus:not(.disabled) {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}

.test-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-lg;
}

.test-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  
  &.icon-PDQ5 {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  }
  
  &.icon-Hopkins {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
  }
  
  &.icon-NBack {
    background: linear-gradient(135deg, #45b7d1, #96c93d);
  }
  
  &.icon-Stroop {
    background: linear-gradient(135deg, #f093fb, #f5576c);
  }
  
  &.icon-Trail {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
  }
  
  &.icon-Verbal {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
  }
  
  &.icon-CPT {
    background: linear-gradient(135deg, #fa709a, #fee140);
  }
  
  &.icon-DSST {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
  }
  
  &.icon-Masked {
    background: linear-gradient(135deg, #667eea, #764ba2);
  }
}

.disabled-badge {
  background: var(--color-muted);
  color: var(--color-muted-foreground);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
}

.test-info {
  margin-bottom: $spacing-lg;
}

.test-name {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
  margin-bottom: $spacing-sm;
}

.test-description {
  color: var(--color-muted-foreground);
  line-height: 1.5;
  margin-bottom: $spacing-md;
}

.test-meta {
  display: flex;
  gap: $spacing-lg;
  margin-bottom: $spacing-md;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
}

.test-features {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
}

.feature-tag {
  background: var(--color-muted);
  color: var(--color-muted-foreground);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
}

.test-action {
  border-top: 1px solid var(--color-border);
  padding-top: $spacing-md;
}

.start-btn {
  width: 100%;
  background: var(--color-primary);
  color: var(--color-primary-foreground);
  border: none;
  border-radius: var(--radius);
  padding: $spacing-md;
  font-size: $font-size-base;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(.disabled) {
    background: oklch(from var(--color-primary) calc(l - 0.05) c h);
  }
  
  &.disabled {
    background: var(--color-muted);
    color: var(--color-muted-foreground);
    cursor: not-allowed;
  }
}

.guidelines-section {
  margin-bottom: $spacing-xl;
}

.guidelines-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: $spacing-xl;
}

.guidelines-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
  margin-bottom: $spacing-lg;
  text-align: center;
}

.guidelines-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: $spacing-lg;
}

.guideline-item {
  display: flex;
  gap: $spacing-md;
}

.guideline-icon {
  width: 40px;
  height: 40px;
  background: oklch(from var(--color-primary) l c h / 0.1);
  color: var(--color-primary);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.guideline-text {
  h4 {
    font-size: $font-size-base;
    font-weight: 600;
    color: var(--color-foreground);
    margin-bottom: $spacing-xs;
  }
  
  p {
    font-size: $font-size-sm;
    color: var(--color-muted-foreground);
    line-height: 1.5;
  }
}

// 响应式设计
@media (max-width: $tablet) {
  .test-content {
    padding: $spacing-lg $spacing-md;
  }
  
  .tests-grid {
    grid-template-columns: 1fr;
  }
  
  .test-meta {
    flex-direction: column;
    gap: $spacing-sm;
  }
  
  .guidelines-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: $mobile) {
  .test-card {
    padding: $spacing-lg;
  }
  
  .test-icon {
    width: 40px;
    height: 40px;
  }
  
  .intro-card {
    padding: $spacing-xl;
  }
}
</style>