@use './variables.scss' as *;

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  min-height: 100%;
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background);
  color: var(--foreground);
}

// 无障碍 - 仅屏幕阅读器可见
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 焦点样式
*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

// 触摸优化
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 按钮基础样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius);
  font-size: $font-size-base;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
  text-decoration: none;
  min-height: 44px;
  padding: 12px 24px;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);

    &:hover:not(:disabled) {
      background-color: oklch(from var(--primary) calc(l - 0.05) c h);
    }
  }

  &.btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);

    &:hover:not(:disabled) {
      background-color: oklch(from var(--secondary) calc(l - 0.05) c h);
    }
  }

  &.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border);
    color: var(--foreground);

    &:hover:not(:disabled) {
      background-color: var(--muted);
    }
  }

  &.btn-ghost {
    background-color: transparent;
    color: var(--foreground);

    &:hover:not(:disabled) {
      background-color: var(--muted);
    }
  }
}

// 卡片样式
.card {
  background-color: var(--card);
  color: var(--card-foreground);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  padding: $spacing-lg;
}

// 输入框样式
.input {
  width: 100%;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  background-color: var(--input);
  padding: 12px 16px;
  font-size: $font-size-base;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px oklch(from var(--primary) l c h / 0.2);
  }

  &::placeholder {
    color: var(--muted-foreground);
  }
}

// 标签样式
.label {
  display: block;
  font-size: $font-size-sm;
  font-weight: 500;
  color: var(--foreground);
  margin-bottom: $spacing-sm;
}

// 响应式网格
.grid {
  display: grid;
  gap: $spacing-md;
  
  &.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  &.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  &.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  &.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

// Flex布局工具
.flex {
  display: flex;
  
  &.flex-col { flex-direction: column; }
  &.items-center { align-items: center; }
  &.justify-center { justify-content: center; }
  &.justify-between { justify-content: space-between; }
  &.gap-2 { gap: $spacing-sm; }
  &.gap-4 { gap: $spacing-md; }
  &.gap-6 { gap: $spacing-lg; }
}

// 文本样式
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }
.text-3xl { font-size: $font-size-3xl; }

// 颜色工具类
.text-primary { color: var(--primary); }
.text-secondary { color: var(--secondary); }
.text-muted { color: var(--muted-foreground); }
.text-destructive { color: var(--destructive); }

// 间距工具类
.p-2 { padding: $spacing-sm; }
.p-4 { padding: $spacing-md; }
.p-6 { padding: $spacing-lg; }
.px-4 { padding-left: $spacing-md; padding-right: $spacing-md; }
.py-2 { padding-top: $spacing-sm; padding-bottom: $spacing-sm; }
.py-4 { padding-top: $spacing-md; padding-bottom: $spacing-md; }

.m-2 { margin: $spacing-sm; }
.m-4 { margin: $spacing-md; }
.mb-2 { margin-bottom: $spacing-sm; }
.mb-4 { margin-bottom: $spacing-md; }
.mt-2 { margin-top: $spacing-sm; }
.mt-4 { margin-top: $spacing-md; }

// 平板特定样式
@media (min-width: $tablet) {
  .tablet-container {
    max-width: 1024px;
    margin: 0 auto;
    padding: $spacing-xl;
  }
}

// 动画
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// 加载动画
@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading {
  animation: spin 1s linear infinite;
}