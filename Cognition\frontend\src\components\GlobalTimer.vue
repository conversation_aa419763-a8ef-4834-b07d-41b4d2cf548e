<template>
  <div v-if="isRunning" class="global-timer">
    <div class="timer-content">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
        <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span class="timer-text">{{ formattedTime }}</span>
      <span v-if="currentTask" class="task-name">{{ currentTask }}</span>
    </div>
    
    <!-- 控制按钮（可选，用于调试） -->
    <div v-if="showControls" class="timer-controls">
      <button @click="pause" v-if="isRunning" class="control-btn">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="6" y="4" width="4" height="16" fill="currentColor"/>
          <rect x="14" y="4" width="4" height="16" fill="currentColor"/>
        </svg>
      </button>
      <button @click="resume" v-else class="control-btn">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <polygon points="5,3 19,12 5,21" fill="currentColor"/>
        </svg>
      </button>
      <button @click="stop" class="control-btn stop-btn">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="3" y="3" width="18" height="18" fill="currentColor"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGlobalTimer } from '@/utils/globalTimer'

interface Props {
  showControls?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'inline' | 'title-left'
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  showControls: false,
  position: 'top-right',
  size: 'medium'
})

const {
  isRunning,
  elapsedTime,
  currentTask,
  formattedTime,
  pause,
  resume,
  stop
} = useGlobalTimer()
</script>

<style lang="scss" scoped>
.global-timer {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  background: var(--card);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-sm $spacing-md;
  box-shadow: var(--shadow-sm);
  font-weight: 600;
  color: var(--foreground);
  z-index: 1000;

  // 位置样式
  &.top-right {
    position: fixed;
    top: $spacing-md;
    right: $spacing-md;
  }

  &.top-left {
    position: fixed;
    top: $spacing-md;
    left: $spacing-md;
  }

  &.bottom-right {
    position: fixed;
    bottom: $spacing-md;
    right: $spacing-md;
  }

  &.bottom-left {
    position: fixed;
    bottom: $spacing-md;
    left: $spacing-md;
  }

  &.inline {
    position: relative;
  }

  &.title-left {
    position: absolute;
    left: $spacing-md;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }

  // 尺寸样式
  &.small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
    
    .timer-content {
      gap: $spacing-xs;
    }
  }

  &.medium {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }

  &.large {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-base;
  }
}

.timer-content {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.timer-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  letter-spacing: 0.5px;
  color: var(--primary);
  font-weight: 700;
}

.task-name {
  color: var(--muted-foreground);
  font-size: 0.9em;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.timer-controls {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  margin-left: $spacing-xs;
  padding-left: $spacing-xs;
  border-left: 1px solid var(--border);
}

.control-btn {
  background: transparent;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: $spacing-xs;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--muted);
    color: var(--foreground);
  }

  &.stop-btn {
    color: var(--destructive);

    &:hover {
      background: var(--destructive);
      color: var(--destructive-foreground);
    }
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .global-timer {
    &.top-right,
    &.top-left {
      top: $spacing-sm;
      right: $spacing-sm;
      left: auto;
    }

    &.bottom-right,
    &.bottom-left {
      bottom: $spacing-sm;
      right: $spacing-sm;
      left: auto;
    }

    &.title-left {
      left: $spacing-xs;
    }

    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;

    .task-name {
      display: none; // 移动端隐藏任务名称
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.global-timer {
  animation: fadeIn 0.3s ease-out;
}
</style>
