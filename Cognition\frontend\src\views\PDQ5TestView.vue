<template>
  <div class="test-container">
    <!-- 头部进度条 -->
    <div class="header-section">
      <button @click="handleExit" class="exit-button" aria-label="退出测试">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

      <div class="header-center">
        <!-- 全局计时器 -->
        <GlobalTimer position="title-left" size="medium" />

        <div class="progress-info">
          <h1 class="test-title">{{ currentSection?.name || 'PDQ-5测试' }}</h1>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progressPercentage}%` }"></div>
          </div>
          <div class="progress-text">
            {{ (currentQuestionIndex - getSectionStartIndex(currentSectionIndex)) + 1 }} / {{ currentSection?.questions.length || 0 }} 题
          </div>
        </div>
      </div>

      <div class="header-right">
        <!-- 主题切换按钮 -->
        <ThemeToggle />
      </div>
    </div>

    <!-- 测试内容 -->
    <div class="test-content">
      <!-- 分节说明 -->
      <div v-if="showSectionIntro" class="section-intro">
        <div class="section-card">
          <div class="section-header">
            <h2 class="section-title">{{ currentSection?.name }}</h2>
            <button
              v-if="speechSupported"
              @click="speakSectionIntro"
              class="speech-button"
              :class="{ active: isSpeaking }"
              :disabled="isLoading"
              aria-label="语音播报"
            >
              <svg v-if="!isSpeaking" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19.07 4.93A10 10 0 0 1 19.07 19.07M15.54 8.46A5 5 0 0 1 15.54 15.54" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M23 9L17 15M17 9L23 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
          <p class="section-description">{{ currentSection?.description }}</p>
          <div class="section-instructions">
            <p>{{ currentSection?.instructions }}</p>
          </div>
          
          <div class="options-preview">
            <h3 class="options-title">评分标准：</h3>
            <div class="options-list">
              <div
                v-for="option in currentSection?.options"
                :key="option.value"
                class="option-preview"
              >
                <span class="option-value">{{ option.value }}</span>
                <span class="option-text">{{ option.text }}</span>
              </div>
            </div>
          </div>

          <button @click="startSection" class="start-section-button">
            开始测试
          </button>
        </div>
      </div>

      <!-- 问题内容 -->
      <div v-else class="question-content">
        <div class="question-card">
          <div class="question-header">
            <div class="question-number">第 {{ currentSectionQuestionNumber }} 题</div>
            <button 
              v-if="speechSupported" 
              @click="speakQuestion" 
              class="speech-button"
              :class="{ active: isSpeaking }"
              aria-label="语音播报题目"
            >
              <svg v-if="!isSpeaking" width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19.07 4.93A10 10 0 0 1 19.07 19.07M15.54 8.46A5 5 0 0 1 15.54 15.54" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M23 9L17 15M17 9L23 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
          
          <div class="question-text">
            {{ currentQuestion?.text }}
          </div>

          <div class="options-container">
            <div 
              v-for="option in currentQuestion?.options" 
              :key="option.value"
              class="option-item"
              :class="{ selected: selectedAnswer === option.value }"
              @click="selectAnswer(option.value)"
            >
              <div class="option-radio">
                <div class="radio-dot" v-if="selectedAnswer === option.value"></div>
              </div>
              <div class="option-content">
                <div class="option-main">
                  <span class="option-value">{{ option.value }}</span>
                  <span class="option-text">{{ option.text }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="question-actions">
            <button 
              @click="previousQuestion" 
              class="nav-button prev-button"
              :disabled="currentQuestionIndex === 0"
            >
              上一题
            </button>
            
            <button 
              @click="nextQuestion" 
              class="nav-button next-button"
              :disabled="selectedAnswer === null"
            >
              {{ isLastQuestion ? '完成测试' : '下一题' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 退出确认对话框 -->
    <div v-if="showExitDialog" class="modal-overlay" @click="cancelExit">
      <div class="modal-content" @click.stop>
        <h3 class="modal-title">确认退出测试？</h3>
        <p class="modal-message">退出后当前进度将会丢失，确定要退出吗？</p>
        <div class="modal-actions">
          <button @click="cancelExit" class="modal-button cancel-button">取消</button>
          <button @click="confirmExit" class="modal-button confirm-button">确认退出</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { pdq5TestData } from '@/data/pdq5-test'
import { speechService, SpeechService } from '@/utils/speech'
import type { TestAnswer } from '@/types/test'
import { GlobalTimerService } from '@/utils/globalTimer'
import ThemeToggle from '@/components/ThemeToggle.vue'
import GlobalTimer from '@/components/GlobalTimer.vue'

const router = useRouter()

// 响应式数据
const currentSectionIndex = ref(0)
const currentQuestionIndex = ref(0)
const selectedAnswer = ref<number | null>(null)
const answers = ref<TestAnswer[]>([])
const showSectionIntro = ref(true)
const showExitDialog = ref(false)
const isSpeaking = ref(false)
const speechSupported = ref(SpeechService.isSupported())
const isLoading = ref(false)



// 测试数据
const testData = pdq5TestData
const totalQuestions = testData.totalQuestions

// 计算属性
const currentSection = computed(() => testData.sections[currentSectionIndex.value])
const currentQuestion = computed(() => {
  if (!currentSection.value || showSectionIntro.value) return null
  const sectionQuestionIndex = currentQuestionIndex.value - getSectionStartIndex(currentSectionIndex.value)
  return currentSection.value.questions[sectionQuestionIndex]
})

const progressPercentage = computed(() => {
  if (!currentSection.value) return 0

  // 计算当前分节的进度
  const sectionStartIndex = getSectionStartIndex(currentSectionIndex.value)
  const currentSectionQuestionIndex = currentQuestionIndex.value - sectionStartIndex
  const sectionQuestionCount = currentSection.value.questions.length

  return Math.round((currentSectionQuestionIndex / sectionQuestionCount) * 100)
})

const isLastQuestion = computed(() => {
  return currentQuestionIndex.value === totalQuestions - 1
})

// 当前小节内的题目编号（从1开始）
const currentSectionQuestionNumber = computed(() => {
  if (!currentSection.value || showSectionIntro.value) return 0
  const sectionStartIndex = getSectionStartIndex(currentSectionIndex.value)
  return currentQuestionIndex.value - sectionStartIndex + 1
})



// 获取分节开始的题目索引
const getSectionStartIndex = (sectionIndex: number): number => {
  let startIndex = 0
  for (let i = 0; i < sectionIndex; i++) {
    startIndex += testData.sections[i].questions.length
  }
  return startIndex
}





// 开始当前分节
const startSection = () => {
  showSectionIntro.value = false
  // 全局计时器会自动继续，不需要额外处理
}

// 选择答案
const selectAnswer = (value: number) => {
  selectedAnswer.value = value

  // 延迟500ms后自动进入下一题
  setTimeout(() => {
    if (selectedAnswer.value === value) { // 确保答案没有被改变
      nextQuestion()
    }
  }, 500)
}

// 语音播报分节介绍
const speakSectionIntro = async () => {
  if (!currentSection.value) return

  if (isSpeaking.value) {
    speechService.stop()
    isSpeaking.value = false
  } else {
    try {
      isSpeaking.value = true
      const introText = `${currentSection.value.description}。${currentSection.value.instructions}`
      await speechService.speak(introText)
      isSpeaking.value = false
    } catch (error) {
      console.error('语音播报失败:', error)
      isSpeaking.value = false
    }
  }
}

// 语音播报题目
const speakQuestion = async () => {
  if (!currentQuestion.value) return

  if (isSpeaking.value) {
    speechService.stop()
    isSpeaking.value = false
  } else {
    try {
      isSpeaking.value = true
      await speechService.speak(currentQuestion.value.text)
      isSpeaking.value = false
    } catch (error) {
      console.error('语音播报失败:', error)
      isSpeaking.value = false
    }
  }
}

// 下一题
const nextQuestion = () => {
  if (selectedAnswer.value === null) return

  // 保存答案
  if (currentQuestion.value) {
    const existingAnswerIndex = answers.value.findIndex(
      answer => answer.questionId === currentQuestion.value!.id
    )
    
    const newAnswer: TestAnswer = {
      questionId: currentQuestion.value.id,
      value: selectedAnswer.value,
      timestamp: Date.now()
    }

    if (existingAnswerIndex >= 0) {
      answers.value[existingAnswerIndex] = newAnswer
    } else {
      answers.value.push(newAnswer)
    }
  }

  // 检查是否完成测试
  if (isLastQuestion.value) {
    completeTest()
    return
  }

  // 移动到下一题
  currentQuestionIndex.value++
  selectedAnswer.value = null

  // 检查是否需要切换到下一个分节
  const nextSectionIndex = getCurrentSectionFromQuestionIndex(currentQuestionIndex.value)
  if (nextSectionIndex !== currentSectionIndex.value) {
    currentSectionIndex.value = nextSectionIndex
    showSectionIntro.value = true
  }
}

// 上一题
const previousQuestion = () => {
  if (currentQuestionIndex.value === 0) return

  currentQuestionIndex.value--
  
  // 检查是否需要切换到上一个分节
  const prevSectionIndex = getCurrentSectionFromQuestionIndex(currentQuestionIndex.value)
  if (prevSectionIndex !== currentSectionIndex.value) {
    currentSectionIndex.value = prevSectionIndex
    showSectionIntro.value = false
  }

  // 恢复之前的答案
  if (currentQuestion.value) {
    const existingAnswer = answers.value.find(
      answer => answer.questionId === currentQuestion.value!.id
    )
    selectedAnswer.value = existingAnswer?.value || null
  }
}

// 根据题目索引获取对应的分节索引
const getCurrentSectionFromQuestionIndex = (questionIndex: number): number => {
  let totalQuestions = 0
  for (let i = 0; i < testData.sections.length; i++) {
    totalQuestions += testData.sections[i].questions.length
    if (questionIndex < totalQuestions) {
      return i
    }
  }
  return testData.sections.length - 1
}

// 完成测试
const completeTest = () => {
  speechService.stop()

  // 这里可以保存测试结果到后端
  console.log('测试完成，答案：', answers.value)
  console.log('测试用时：', GlobalTimerService.getFormattedTime())

  // 停止全局计时器（PDQ5测试完成）
  GlobalTimerService.stop()

  // 跳转到结果页面或返回首页
  router.push('/home')
}

// 退出测试
const handleExit = () => {
  showExitDialog.value = true
}

const cancelExit = () => {
  showExitDialog.value = false
}

const confirmExit = () => {
  // 停止全局计时器（用户主动退出）
  GlobalTimerService.stop()
  speechService.stop()
  router.push('/home')
}

// 监听题目变化，重置选择
watch(currentQuestion, (newQuestion) => {
  if (newQuestion && !showSectionIntro.value) {
    const existingAnswer = answers.value.find(
      answer => answer.questionId === newQuestion.id
    )
    selectedAnswer.value = existingAnswer?.value || null
  }
})

// 组件卸载时停止语音（但不停止全局计时器，除非测试真正结束）
onUnmounted(() => {
  speechService.stop()
})
</script>

<style lang="scss" scoped>
.test-container {
  height: 100vh;
  background: var(--background);
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止滚动条
}

.header-section {
  background: var(--card);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  padding: $spacing-md 2.5%; // 与答题卡片对齐的左右边距
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  box-shadow: var(--shadow-lg);
  position: relative;
  flex-shrink: 0; // 防止头部被压缩
}

.exit-button {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-sm;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  order: -1; // 确保退出按钮在最左侧

  &:hover {
    background: var(--muted);
    color: var(--destructive);
    transform: translateY(-1px);
  }
}

.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  justify-content: center;
}

.progress-info {
  flex: 1;
  text-align: center;
}

.test-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--foreground);
  margin: 0 0 $spacing-sm 0;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: var(--muted);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: $spacing-sm;
  border: 1px solid var(--border);
  box-shadow: inset 0 2px 4px var(--shadow-sm);
}

.progress-fill {
  height: 100%;
  background: var(--primary);
  border-radius: 6px;
  transition: width 0.3s ease;
  box-shadow: var(--shadow-md);
}

.progress-text {
  font-size: $font-size-base;
  color: var(--muted-foreground);
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-shrink: 0;
}



.test-content {
  flex: 1;
  padding: $spacing-sm;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; // 防止内容溢出
}

.section-intro {
  width: 95%;
  max-width: none;
}

.section-card {
  background: var(--card);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
  padding: $spacing-lg;
  animation: fadeIn 0.6s ease-out;
  height: 70vh; // 保持固定高度
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow-y: auto; // 允许垂直滚动
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
}

.section-title {
  font-size: $font-size-xl;
  font-weight: 700;
  color: var(--foreground);
  margin: 0;
  flex: 1;
}

.speech-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid var(--primary);
  background: var(--card);
  color: var(--primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:hover {
    background: var(--primary);
    color: var(--primary-foreground);
    transform: scale(1.05);
  }

  &.active {
    background: var(--primary);
    color: var(--primary-foreground);
    animation: pulse 1.5s infinite;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.section-description {
  font-size: $font-size-xl;
  color: var(--muted-foreground);
  text-align: center;
  margin: 0 0 $spacing-md 0;
  line-height: 1.6;
}

.section-instructions {
  background: var(--muted);
  border-radius: 16px;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  border-left: 4px solid var(--primary);

  p {
    margin: 0;
    font-size: $font-size-base;
    color: var(--foreground);
    line-height: 1.7;
  }
}

.options-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; // 改为顶部对齐
  margin-bottom: $spacing-md;
  overflow-y: auto; // 允许选项区域滚动
  min-height: 0; // 确保可以收缩
}

.options-title {
  font-size: $font-size-base;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 $spacing-md 0;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.option-preview {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-sm $spacing-md; // 减少垂直内边距
  background: var(--card);
  border-radius: 12px;
  border: 1px solid oklch(0.92 0.005 240);
}

.option-value {
  width: 24px;
  height: 24px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: $font-size-sm;
  flex-shrink: 0;
}

.option-text {
  font-weight: 600;
  color: var(--foreground);
  min-width: 60px;
  font-size: 20px;
}



.start-section-button {
  width: 100%;
  background: var(--primary);
  border: none;
  border-radius: 16px;
  padding: 16px 32px;
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--primary-foreground);
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0; // 防止按钮被压缩
  margin-top: auto; // 将按钮推到底部
  box-shadow: var(--shadow-lg);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }
}

.question-content {
  width: 95%;
  max-width: none;
}

.question-card {
  background: var(--card);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
  padding: $spacing-lg;
  animation: fadeIn 0.6s ease-out;
  height: 70vh; // 固定高度，保持答题卡片大小不变
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止内容溢出
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-xs; // 减少底部边距
  flex-shrink: 0; // 防止头部被压缩
}

.question-number {
  background: var(--primary);
  color: var(--primary-foreground);
  padding: $spacing-xs $spacing-md;
  border-radius: 20px;
  font-weight: 600;
  font-size: $font-size-sm;
}

.speech-button {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-sm;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--muted);
    color: var(--primary);
    transform: translateY(-1px);
  }

  &.active {
    background: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
  }
}

.question-text {
  font-size: 32px;
  font-weight: 600;
  color: var(--foreground);
  line-height: 1.4;
  margin-bottom: $spacing-sm; // 减少底部边距
  padding: $spacing-sm $spacing-md; // 减少内边距
  background: var(--muted);
  border-radius: 12px;
  border-left: 4px solid var(--primary);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0; // 防止问题文本被压缩
}

.options-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: space-evenly;
  gap: $spacing-xs; // 进一步减少选项间距
  margin-bottom: $spacing-xs; // 进一步减少底部边距
  padding: $spacing-xs 0; // 减少内边距
  overflow: hidden; // 防止滚动条出现
  min-height: 0; // 确保flex子项可以收缩
}

.option-item {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-sm $spacing-md; // 减少垂直内边距以适应5个选项
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--muted);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &.selected {
    background: var(--card);
    border-color: var(--primary);
    box-shadow: var(--shadow-lg);
  }
}

.option-radio {
  width: 20px;
  height: 20px;
  border: 2px solid oklch(0.88 0.005 240);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;

  .option-item.selected & {
    border-color: var(--primary);
    background: var(--primary);
  }
}

.radio-dot {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.option-content {
  flex: 1;
}

.option-main {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-xs;

  .option-value {
    width: 32px;
    height: 32px;
    background: var(--muted);
    color: var(--foreground);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: $font-size-sm;
    flex-shrink: 0;
    transition: all 0.3s ease;

    .option-item.selected & {
      background: var(--primary);
      color: white;
    }
  }

  .option-text {
    font-weight: 600;
    color: var(--foreground);
    font-size: 20px;
  }
}

.option-description {
  color: var(--muted-foreground);
  font-size: $font-size-sm;
  line-height: 1.4;
  margin-left: 40px;
}

.question-actions {
  display: flex;
  justify-content: space-between;
  gap: $spacing-md;
  padding-top: $spacing-sm; // 减少顶部内边距
  border-top: 1px solid oklch(0.92 0.005 240);
  margin-top: auto;
  flex-shrink: 0; // 防止按钮区域被压缩
}

.nav-button {
  padding: 10px 20px; // 减少内边距
  border-radius: 12px;
  font-weight: 600;
  font-size: $font-size-base;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.prev-button {
  background: var(--card);
  color: var(--muted-foreground);
  border: 1px solid var(--border);

  &:hover:not(:disabled) {
    background: var(--muted);
    color: var(--foreground);
    transform: translateY(-1px);
  }
}

.next-button {
  background: linear-gradient(135deg, var(--primary), oklch(0.7 0.12 240));
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
}

// 模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background: white;
  border-radius: 20px;
  padding: $spacing-2xl;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

.modal-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 $spacing-md 0;
  text-align: center;
}

.modal-message {
  color: var(--muted-foreground);
  line-height: 1.6;
  margin: 0 0 $spacing-xl 0;
  text-align: center;
}

.modal-actions {
  display: flex;
  gap: $spacing-md;
}

.modal-button {
  flex: 1;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.cancel-button {
  background: var(--card);
  color: var(--muted-foreground);
  border: 1px solid var(--border);

  &:hover {
    background: var(--muted);
    color: var(--foreground);
  }
}

.confirm-button {
  background: var(--destructive);
  color: white;

  &:hover {
    background: oklch(0.75 0.15 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .test-container {
    padding: 0;
  }

  .header-section {
    padding: $spacing-md 2.5%; // 与答题卡片保持一致的边距
  }

  .test-content {
    padding: $spacing-xs;
    // 移除min-height，使用flex布局自动适应
  }

  .question-content,
  .section-intro {
    width: 95%; // Almost full width on mobile
    max-width: none;
  }

  .section-card,
  .question-card {
    padding: $spacing-md; // 减少内边距以适应移动端
    height: 75vh; // 移动端稍微增加高度
  }

  .options-list {
    gap: $spacing-xs;
  }

  .option-preview {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
  }

  .option-item {
    padding: $spacing-md;
  }

  .question-actions {
    flex-direction: column;
  }

  .nav-button {
    width: 100%;
  }
}
</style>
