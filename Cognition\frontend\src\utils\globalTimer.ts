import { ref, computed } from 'vue'

// 全局计时器状态
export interface TimerState {
  isRunning: boolean
  startTime: number | null
  elapsedTime: number
  currentTask: string | null
}

// 响应式状态
const timerState = ref<TimerState>({
  isRunning: false,
  startTime: null,
  elapsedTime: 0,
  currentTask: null
})

let timerInterval: number | null = null

// 全局计时器服务
export class GlobalTimerService {
  // 开始计时
  static start(taskName: string = '认知测试任务') {
    if (!timerState.value.isRunning) {
      timerState.value.startTime = Date.now()
      timerState.value.isRunning = true
      timerState.value.currentTask = taskName
      timerState.value.elapsedTime = 0
      
      // 启动定时器
      timerInterval = window.setInterval(() => {
        if (timerState.value.startTime) {
          timerState.value.elapsedTime = Math.floor((Date.now() - timerState.value.startTime) / 1000)
        }
      }, 1000)
      
      console.log(`全局计时器已启动: ${taskName}`)
    }
  }

  // 暂停计时
  static pause() {
    if (timerState.value.isRunning && timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
      timerState.value.isRunning = false
      console.log('全局计时器已暂停')
    }
  }

  // 恢复计时
  static resume() {
    if (!timerState.value.isRunning && timerState.value.startTime) {
      // 调整开始时间，考虑已经过去的时间
      timerState.value.startTime = Date.now() - (timerState.value.elapsedTime * 1000)
      timerState.value.isRunning = true
      
      timerInterval = window.setInterval(() => {
        if (timerState.value.startTime) {
          timerState.value.elapsedTime = Math.floor((Date.now() - timerState.value.startTime) / 1000)
        }
      }, 1000)
      
      console.log('全局计时器已恢复')
    }
  }

  // 停止计时
  static stop() {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
    
    const finalTime = timerState.value.elapsedTime
    const taskName = timerState.value.currentTask
    
    timerState.value.isRunning = false
    timerState.value.startTime = null
    timerState.value.elapsedTime = 0
    timerState.value.currentTask = null
    
    console.log(`全局计时器已停止: ${taskName}, 总用时: ${finalTime}秒`)
    return finalTime
  }

  // 重置计时器
  static reset() {
    this.stop()
    console.log('全局计时器已重置')
  }

  // 获取当前状态
  static getState() {
    return timerState.value
  }

  // 获取格式化时间
  static getFormattedTime(): string {
    const seconds = timerState.value.elapsedTime
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    } else {
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    }
  }

  // 检查是否正在运行
  static isRunning(): boolean {
    return timerState.value.isRunning
  }

  // 获取当前任务名称
  static getCurrentTask(): string | null {
    return timerState.value.currentTask
  }

  // 获取已用时间（秒）
  static getElapsedTime(): number {
    return timerState.value.elapsedTime
  }
}

// Vue组合式API钩子
export function useGlobalTimer() {
  const isRunning = computed(() => timerState.value.isRunning)
  const elapsedTime = computed(() => timerState.value.elapsedTime)
  const currentTask = computed(() => timerState.value.currentTask)
  const formattedTime = computed(() => GlobalTimerService.getFormattedTime())

  return {
    isRunning,
    elapsedTime,
    currentTask,
    formattedTime,
    start: GlobalTimerService.start,
    pause: GlobalTimerService.pause,
    resume: GlobalTimerService.resume,
    stop: GlobalTimerService.stop,
    reset: GlobalTimerService.reset
  }
}

// 导出响应式状态供直接使用
export { timerState }
